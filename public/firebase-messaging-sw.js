// public/firebase-messaging-sw.js

importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: "AIzaSyAdSz6G-3d_18b0h3AJZwa-gs55nubx5GE",
  authDomain: "aquot-cc946.firebaseapp.com",
  projectId: "aquot-cc946",
  storageBucket: "quot-cc946.firebasestorage.app",
  messagingSenderId: "905848946630",
  appId: "1:905848946630:web:73e732e526b6fe61df7cc7",
});


  

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  // Customize notification here
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/firebase-logo.png'
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
