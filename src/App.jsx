import React, { useState } from 'react';
import axios from 'axios';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';
import { getMessaging, getToken } from 'firebase/messaging';
import './firebase.js'; // Make sure Firebase app is initialized

function FirebaseRegistrationForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    number: '',
    address: '',
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const auth = getAuth();
  const messaging = getMessaging(); // For FCM

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const detectPlatform = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    if (/android/i.test(userAgent)) {
      return 'android';
    }
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      return 'ios';
    }
    return 'web';
  };

  const generateDeviceId = () => {
    // Simple fallback deviceId (could be replaced by a better one later)
    return navigator.userAgent + '_' + Date.now();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      // Create the user in Firebase Authentication
      const userCredential = await createUserWithEmailAndPassword(auth, formData.email, formData.password);
      const user = userCredential.user;
      const uid = user.uid;

      // Get Firebase ID Token
      const idToken = await user.getIdToken();

      // Request Notification permission & get FCM token
      let fcmTokenData = null;
      try {
        await Notification.requestPermission();
        const token = await getToken(messaging, {
          vapidKey: 'BDqTfrdNXmzFlMWWT96zpBYJTlC3E0NXI3ONzcI9xn234jl5h_EtNUz7oyBXaFUjCYLMODa-mT8F8mCl9LtzYp0',
          serviceWorkerRegistration: await navigator.serviceWorker.register('/firebase-messaging-sw.js')

        });

        fcmTokenData = {
          token,
          platform: detectPlatform(),
          deviceId: generateDeviceId(),
          updatedAt: new Date(),
        };

        console.log('FCM Token Data:', fcmTokenData);
      } catch (fcmError) {
        console.warn('FCM token generation failed:', fcmError);
      }

      // Prepare the data to send to backend
      const backendData = {
        uid,
        email: formData.email,
        name: formData.name,
        number: formData.number,
        address: formData.address,
        fcmTokens: fcmTokenData ? [fcmTokenData] : [], // ✅ as an array of token objects
      };

      // Post data to backend
      const response = await axios.post('http://gateway.local/api/auth/firebaseRegister', backendData);
      setMessage(response.data.message || 'User registered successfully!');
      console.log(response.data);
    } catch (error) {
      console.error('Registration error:', error);
      setMessage(error.response?.data?.error || error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: '500px', margin: '2rem auto' }}>
      <h2>Firebase Registration</h2>
      {message && <p>{message}</p>}
      <form onSubmit={handleSubmit}>
        <div>
          <label>Email:</label><br />
          <input type="email" name="email" value={formData.email} onChange={handleChange} required />
        </div>
        <div>
          <label>Password:</label><br />
          <input type="password" name="password" value={formData.password} onChange={handleChange} required />
        </div>
        <div>
          <label>Name:</label><br />
          <input type="text" name="name" value={formData.name} onChange={handleChange} required />
        </div>
        <div>
          <label>Phone Number:</label><br />
          <input type="text" name="number" value={formData.number} onChange={handleChange} required />
        </div>
        <div>
          <label>Address:</label><br />
          <input type="text" name="address" value={formData.address} onChange={handleChange} required />
        </div>
        <button type="submit" disabled={loading}>
          {loading ? 'Registering...' : 'Register'}
        </button>
      </form>
    </div>
  );
}

export default FirebaseRegistrationForm;
